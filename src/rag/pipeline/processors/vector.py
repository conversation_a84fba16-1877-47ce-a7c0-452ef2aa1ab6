"""
向量嵌入处理器

处理实体和文档的向量化任务
"""

from typing import List

from .base import TaskProcessor, register_processor
from ..models import PipelineTask, TaskResult, TaskType


@register_processor(TaskType.VECTOR_EMBEDDING)
class VectorEmbeddingProcessor(TaskProcessor):
    """向量嵌入处理器"""

    def __init__(self, embedding_func=None):
        super().__init__()
        self.embedding_func = embedding_func
        self.embedding_client = None

    async def initialize(self):
        """初始化向量嵌入客户端"""
        if self.embedding_func is None and self.embedding_client is None:
            # 获取embedding函数
            from src.rag.llm import call_embedding
            self.embedding_func = call_embedding

    def get_supported_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return [TaskType.VECTOR_EMBEDDING]

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理向量嵌入任务"""
        try:
            # 初始化embedding客户端
            await self.initialize()

            # 验证输入参数
            if not task.parameters:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="缺少输入参数"
                )

            data_type = task.parameters.get("data_type", "chunks")
            doc_id = task.parameters.get("doc_id", "unknown")

            logger.info(f"⚡ [向量嵌入] 开始向量嵌入: doc_id={doc_id}, data_type={data_type}")
            logger.info(f"⚡ [向量嵌入] 任务参数: {task.parameters}")

            # 从前一个任务获取数据
            if pre_task_result and pre_task_result.result_data:
                logger.info(f"⚡ [向量嵌入] 从前一个任务接收数据: {list(pre_task_result.result_data.keys())}")

            # 根据数据类型处理
            if data_type == "entities":
                return await self._process_entity_embedding(task, pre_task_result)
            elif data_type == "chunks":
                return await self._process_chunk_embedding(task, pre_task_result)
            else:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message=f"不支持的数据类型: {data_type}"
                )

        except Exception as e:
            logger.error(f"💥 [向量嵌入] 向量嵌入处理失败: {e}")
            import traceback
            traceback.print_exc()
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"向量嵌入失败: {str(e)}"
            )

    async def _process_entity_embedding(self, task: PipelineTask) -> TaskResult:
        """处理实体向量嵌入"""
        try:
            # 获取实体数据（应该由流水线调度器准备好）
            entities = task.parameters.get("entities", [])
            
            # 检查是否有实体数据的键，而不是值
            # 如果完全没有这个键，说明数据传递有问题
            if "entities" not in task.parameters:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="缺少实体数据键，流水线调度器应该准备好这些数据"
                )
            
            # 如果实体为空，跳过向量嵌入但返回成功
            if not entities:
                logger.info(f"⚡ [向量嵌入] 实体数据为空，跳过向量嵌入")
                return TaskResult(
                    task_id=task.id,
                    success=True,
                    result_data={
                        "doc_id": task.parameters.get("doc_id"),
                        "entity_count": 0,
                        "embedding_status": "skipped_empty",
                        "message": "实体数据为空，向量嵌入已跳过"
                    }
                )
            
            logger.info(f"⚡ [向量嵌入] 准备为 {len(entities)} 个实体生成向量")

            # 如果没有embedding函数，跳过实际的向量化
            if not self.embedding_func:
                logger.warning("⚡ [向量嵌入] 缺少embedding函数，跳过实际向量化")
                return TaskResult(
                    task_id=task.id,
                    success=True,
                    result_data={
                        "doc_id": task.parameters.get("doc_id"),
                        "entity_count": len(entities),
                        "embedding_status": "skipped",
                        "message": "缺少embedding函数，向量化已跳过"
                    }
                )

            # 生成向量
            embedded_entities = []
            for entity in entities:
                # 兼容不同的数据结构：
                # 1. 来自实体抽取处理器的实体：使用 entity_name 
                # 2. 来自图构建处理器的节点：使用 label
                entity_name = entity.get("entity_name") or entity.get("label", "")
                entity_type = entity.get("entity_type") or entity.get("type", "")
                entity_id = entity.get("entity_id") or entity.get("id", "")
                description = entity.get("description", "")
                
                if not entity_name:
                    logger.warning(f"⚠️ [向量嵌入] 跳过无名实体: {entity}")
                    continue
                
                entity_text = f"{entity_name}: {description}"
                # 调用实际的embedding函数生成向量 - call_embedding期望文本列表
                vectors = await self.embedding_func([entity_text])
                vector = vectors[0] if vectors else []

                # 确保向量是浮点数列表格式
                if hasattr(vector, 'tolist'):
                    vector = vector.tolist()

                # 确保所有元素都是浮点数
                vector = [float(x) for x in vector]
                
                embedded_entities.append({
                    "entity_id": entity_id,
                    "entity_name": entity_name,
                    "entity_type": entity_type,
                    "text": entity_text,
                    "vector": vector,
                    "vector_status": "generated"
                })

            logger.info(f"⚡ [向量嵌入] 实体向量嵌入完成: {len(embedded_entities)} 个实体")

            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": task.parameters.get("doc_id"),
                    "entity_embeddings": embedded_entities,
                    "total_entities": len(embedded_entities),
                    "embedding_dimension": 768  # 假设维度
                }
            )

        except Exception as e:
            logger.error(f"💥 [向量嵌入] 实体向量嵌入失败: {e}")
            import traceback
            traceback.print_exc()
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"实体向量嵌入失败: {str(e)}"
            )

    async def _process_chunk_embedding(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理文档块向量嵌入并存储到Milvus"""
        try:
            doc_id = task.parameters.get("doc_id")
            if not doc_id:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="缺少doc_id参数"
                )

            # 从前一个任务的结果中获取chunks Redis key
            chunks_redis_key = None
            if pre_task_result and pre_task_result.result_data:
                chunks_redis_key = pre_task_result.result_data.get("chunks_redis_key")
                logger.info(f"⚡ [向量嵌入] 从前一个任务获取chunks Redis key: {chunks_redis_key}")

            # 从Redis获取分块数据
            chunks = await self._get_chunks_from_redis_by_key(chunks_redis_key)

            if not chunks:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message=f"无法从Redis获取分块数据: key={chunks_redis_key or task.id}"
                )

            logger.info(f"⚡ [向量嵌入] 从Redis获取到 {len(chunks)} 个文档块")

            # 如果没有embedding函数，跳过实际的向量化
            if not self.embedding_func:
                logger.warning("⚡ [向量嵌入] 缺少embedding函数，跳过实际向量化")
                return TaskResult(
                    task_id=task.id,
                    success=True,
                    result_data={
                        "doc_id": doc_id,
                        "chunk_count": len(chunks),
                        "embedding_status": "skipped",
                        "message": "缺少embedding函数，向量化已跳过"
                    }
                )

            # 生成向量并直接存储到Milvus
            stored_count = await self._embed_and_store_chunks(chunks, doc_id)

            logger.info(f"⚡ [向量嵌入+存储] 完成: {stored_count} 个分块已存储到Milvus")

            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": doc_id,
                    "stored_chunks": stored_count,
                    "embedding_status": "completed_and_stored",
                    "chunks_redis_key": chunks_redis_key,  # 传递给下一个任务
                    "chunk_list": chunks  # 传递chunks数据给下一个任务
                }
            )

        except Exception as e:
            logger.error(f"💥 [向量嵌入] 文档块向量嵌入失败: {e}")
            import traceback
            traceback.print_exc()
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message=f"文档块向量嵌入失败: {str(e)}"
            )

    async def _get_chunks_from_redis(self, redis_key: str) -> list:
        """从Redis set中获取分块数据"""
        try:
            from src.rag.infrastructure import redis_client
            import json

            # 从Redis set中获取所有chunk数据
            # redis_key格式：chunks:doc_id
            redis_client = redis_client.redis
            chunk_set_data = await redis_client.smembers(redis_key)

            if not chunk_set_data:
                logger.warning(f"Redis set中未找到分块数据: {redis_key}")
                return []

            # 解析JSON数据并转换为chunk列表
            chunks = []
            for chunk_json in chunk_set_data:
                try:
                    chunk_data = json.loads(chunk_json)
                    chunks.append(chunk_data)
                except json.JSONDecodeError as e:
                    logger.error(f"解析chunk JSON数据失败: {e}")
                    continue

            # 按order_index排序
            chunks.sort(key=lambda x: x.get("order_index", 0))

            logger.info(f"从Redis set获取到 {len(chunks)} 个分块: {redis_key}")

            # 添加调试信息，显示每个分块的基本信息
            for i, chunk in enumerate(chunks):
                logger.debug(f"  分块 {i}: order_index={chunk.get('order_index')}, task_id={chunk.get('task_id')}, chunk_id={chunk.get('chunk_id')}")

            return chunks

        except Exception as e:
            logger.error(f"从Redis set获取分块数据失败: {e}")
            return []

    async def _get_chunks_from_redis_by_key(self, redis_key: str) -> list:
        """通过指定的Redis key获取分块数据"""
        # 使用相同的逻辑
        return await self._get_chunks_from_redis(redis_key)

    async def _embed_and_store_chunks(self, chunks: list, doc_id: str) -> int:
        """向量化分块并存储到Milvus"""
        try:
            from src.rag.infrastructure import milvus_vector_operations
            from src.rag.infrastructure.db.document_operations import document_operations

            # 获取文档的kb_id
            doc_metadata = await document_operations.get_document_metadata(doc_id)
            kb_id = doc_metadata.get("kb_id") if doc_metadata else None

            if not kb_id:
                logger.warning(f"文档 {doc_id} 缺少kb_id信息")

            stored_count = 0
            batch_data = []
            batch_size = 10  # 批量处理以提高效率

            for chunk in chunks:
                try:
                    chunk_text = chunk.get("content", "")
                    if not chunk_text:
                        logger.warning(f"跳过空内容分块: {chunk.get('chunk_id', 'unknown')}")
                        continue

                    # 生成向量 - call_embedding期望文本列表，返回向量列表
                    vectors = await self.embedding_func([chunk_text])
                    vector = vectors[0] if vectors else []

                    # 确保向量是浮点数列表格式
                    if hasattr(vector, 'tolist'):
                        vector = vector.tolist()

                    # 确保所有元素都是浮点数
                    vector = [float(x) for x in vector]

                    # 确保doc_id正确设置
                    chunk_doc_id = chunk.get("doc_id") or doc_id
                    if not chunk_doc_id:
                        logger.error(f"分块缺少doc_id: {chunk.get('chunk_id', 'unknown')}")
                        continue

                    # 准备存储数据，确保doc_id和kb_id正确传递
                    vector_data = {
                        "id": chunk.get("chunk_id"),
                        "vector": vector,
                        "content": chunk_text,
                        "doc_id": chunk_doc_id,
                        "kb_id": kb_id,
                        "chunk_order_index": chunk.get("order_index", 0),
                        "tokens": chunk.get("token_count", 0)
                    }

                    batch_data.append(vector_data)

                    # 批量存储
                    if len(batch_data) >= batch_size:
                        # 转换为upsert方法需要的格式
                        upsert_data = {
                            item["id"]: {
                                "vector": item["vector"],
                                "content": item["content"],
                                "doc_id": item["doc_id"],
                                "kb_id": item["kb_id"],
                                "chunk_order_index": item["chunk_order_index"],
                                "tokens": item["tokens"],
                            } for item in batch_data
                        }
                        await milvus_vector_operations.upsert(upsert_data)
                        stored_count += len(batch_data)
                        logger.debug(f"⚡ [向量存储] 批量存储了 {len(batch_data)} 个向量到Milvus")
                        batch_data = []

                except Exception as e:
                    logger.error(f"处理分块失败: {chunk.get('chunk_id', 'unknown')}, 错误: {e}")
                    continue

            # 存储剩余的数据
            if batch_data:
                # 转换为upsert方法需要的格式
                upsert_data = {
                    item["id"]: {
                        "vector": item["vector"],
                        "content": item["content"],
                        "doc_id": item["doc_id"],
                        "kb_id": item["kb_id"],
                        "chunk_order_index": item["chunk_order_index"],
                        "tokens": item["tokens"],
                    } for item in batch_data
                }
                await milvus_vector_operations.upsert(upsert_data)
                stored_count += len(batch_data)
                logger.debug(f"⚡ [向量存储] 最后批量存储了 {len(batch_data)} 个向量到Milvus")

            # 不需要调用 finalize，连接池会自动管理连接
            
            logger.info(f"⚡ [向量存储] 成功存储 {stored_count} 个分块向量到Milvus, doc_id: {doc_id}")
            return stored_count

        except Exception as e:
            logger.error(f"向量化和存储失败: {e}")
            import traceback
            traceback.print_exc()
            return 0


"""
向量存储处理器

处理向量存储任务，将向量嵌入结果保存到Milvus数据库
"""

from typing import List

from src.rag.tools import logger
from .base import TaskProcessor, register_processor
from ..models import PipelineTask, TaskResult, TaskType


@register_processor(TaskType.VECTOR_STORAGE)
class VectorStorageProcessor(TaskProcessor):
    """向量存储处理器"""

    def __init__(self, vector_storage=None, embedding_func=None):
        super().__init__()
        self.vector_storage = vector_storage
        self.embedding_func = embedding_func

    async def initialize(self):
        """初始化向量存储客户端"""
        if self.vector_storage is None:
            # 使用全局的Milvus向量存储实例
            from src.rag.infrastructure import milvus_vector_operations
            self.vector_storage = milvus_vector_operations

    def get_supported_task_types(self) -> List[TaskType]:
        """获取支持的任务类型"""
        return [TaskType.VECTOR_STORAGE]

    async def process(self, task: PipelineTask, pre_task_result: TaskResult = None) -> TaskResult:
        """处理向量存储任务"""
        # 验证输入参数
        if not task.parameters:
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message="缺少输入参数"
            )

        doc_id = task.parameters.get("doc_id", "unknown")

        logger.info(f"🗄️ [向量存储] 开始处理: doc_id={doc_id}")

        # 从前一个任务获取数据
        if pre_task_result and pre_task_result.result_data:
            logger.info(f"🗄️ [向量存储] 从前一个任务接收数据: {list(pre_task_result.result_data.keys())}")

            # 检查向量嵌入是否已完成
            embedding_status = pre_task_result.result_data.get("embedding_status")
            if embedding_status == "completed_and_stored":
                logger.info(f"🗄️ [向量存储] 向量已在前一步骤中存储完成")

                return TaskResult(
                    task_id=task.id,
                    success=True,
                    result_data={
                        "doc_id": doc_id,
                        "stored_chunks": pre_task_result.result_data.get("stored_chunks", 0),
                        "storage_status": "already_completed",
                        "chunk_list": pre_task_result.result_data.get("chunk_list", []),  # 传递给下一个任务
                        "chunks_redis_key": pre_task_result.result_data.get("chunks_redis_key")  # 传递Redis key
                    }
                )
            else:
                return TaskResult(
                    task_id=task.id,
                    success=False,
                    error_message="前一个任务的向量嵌入未完成"
                )
        else:
            return TaskResult(
                task_id=task.id,
                success=False,
                error_message="缺少前一个任务的输出数据"
            )

    async def _process_entity_storage(self, task: PipelineTask) -> TaskResult:
        """处理实体向量存储"""
        # 获取实体嵌入数据
        entity_embeddings = task.parameters.get("entity_embeddings", [])

        if not entity_embeddings:
            logger.info(f"🗄️ [向量存储] 实体嵌入数据为空，跳过存储")
            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": task.parameters.get("doc_id"),
                    "stored_count": 0,
                    "storage_status": "skipped_empty",
                    "message": "实体嵌入数据为空，向量存储已跳过"
                }
            )

        logger.info(f"🗄️ [向量存储] 准备存储 {len(entity_embeddings)} 个实体向量")

        stored_count = 0
        for entity_embedding in entity_embeddings:
            entity_id = entity_embedding.get("entity_id")
            entity_name = entity_embedding.get("entity_name")
            entity_type = entity_embedding.get("entity_type")
            text = entity_embedding.get("text", "")

            if not entity_id or not text:
                logger.warning(f"⚠️ [向量存储] 跳过无效实体: {entity_embedding}")
                continue

            # 生成向量（如果还没有的话）
            if "vector" in entity_embedding:
                vector = entity_embedding["vector"]
            else:
                # 使用embedding函数生成向量 - call_embedding期望文本列表
                vectors = await self.embedding_func([text])
                vector = vectors[0] if vectors else []

                # 确保向量是浮点数列表格式
                if hasattr(vector, 'tolist'):
                    vector = vector.tolist()

                # 确保所有元素都是浮点数
                vector = [float(x) for x in vector]

            # 存储到Milvus
            await self.vector_storage.upsert_vector(
                vector_id=entity_id,
                vector=vector,
                text=text,
            )

            stored_count += 1

        logger.info(f"🗄️ [向量存储] 实体向量存储完成: {stored_count}/{len(entity_embeddings)} 个实体")

        return TaskResult(
            task_id=task.id,
            success=True,
            result_data={
                "doc_id": task.parameters.get("doc_id"),
                "stored_count": stored_count,
                "total_entities": len(entity_embeddings),
                "storage_status": "completed",
                "storage_type": "entity_vectors"
            }
        )

    async def _process_chunk_storage(self, task: PipelineTask) -> TaskResult:
        """处理文档块向量存储"""
        # 获取文档块嵌入数据
        chunk_embeddings = task.parameters.get("chunk_embeddings", [])

        if not chunk_embeddings:
            logger.info(f"🗄️ [向量存储] 文档块嵌入数据为空，跳过存储")
            return TaskResult(
                task_id=task.id,
                success=True,
                result_data={
                    "doc_id": task.parameters.get("doc_id"),
                    "stored_count": 0,
                    "storage_status": "skipped_empty",
                    "message": "文档块嵌入数据为空，向量存储已跳过"
                }
            )

        logger.info(f"🗄️ [向量存储] 准备存储 {len(chunk_embeddings)} 个文档块向量")

        stored_count = 0
        for chunk_embedding in chunk_embeddings:
            chunk_id = chunk_embedding.get("chunk_id")
            doc_id = chunk_embedding.get("doc_id")
            content = chunk_embedding.get("content", "")
            token_count = chunk_embedding.get("token_count", 0)

            if not chunk_id or not content:
                logger.warning(f"⚠️ [向量存储] 跳过无效文档块: {chunk_embedding}")
                continue

            # 生成向量（如果还没有的话）
            if "vector" in chunk_embedding:
                vector = chunk_embedding["vector"]
            else:
                # 使用embedding函数生成向量 - call_embedding期望文本列表
                vectors = await self.embedding_func([content])
                vector = vectors[0] if vectors else []

                # 确保向量是浮点数列表格式
                if hasattr(vector, 'tolist'):
                    vector = vector.tolist()

                # 确保所有元素都是浮点数
                vector = [float(x) for x in vector]

            # 存储到Milvus
            await self.vector_storage.upsert_vector(
                vector_id=chunk_id,
                vector=vector,
                text=content,
            )

            stored_count += 1

        logger.info(f"🗄️ [向量存储] 文档块向量存储完成: {stored_count}/{len(chunk_embeddings)} 个文档块")

        return TaskResult(
            task_id=task.id,
            success=True,
            result_data={
                "doc_id": task.parameters.get("doc_id"),
                "stored_count": stored_count,
                "total_chunks": len(chunk_embeddings),
                "storage_status": "completed",
                "storage_type": "chunk_vectors"
            }
        )

    async def finalize(self):
        """清理资源"""
        if self.vector_storage:
            await self.vector_storage.finalize()
