"""
文本分块模块

该模块负责将长文档分割成适合处理的文本块，主要功能包括：

1. 智能文本分块
   - 基于token数量进行分块
   - 支持块间重叠以保持上下文连续性
   - 保持段落和句子的完整性

2. 分块策略
   - 优先在段落边界分割
   - 其次在句子边界分割
   - 最后在词语边界分割

3. 分块元数据管理
   - 为每个分块生成唯一ID
   - 记录分块在原文档中的位置
   - 计算分块的token数量

4. 异步处理支持
   - 支持大文档的异步分块处理
   - 提供进度回调机制
"""

import asyncio
import re
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Callable

from src.rag.tools import (
    logger,
    encode_string_by_tiktoken,
    decode_tokens_by_tiktoken,
    compute_mdhash_id
)


@dataclass
class DocumentChunk:
    """
    文档块数据结构

    表示文档分块后的单个文本块，包含内容和元数据信息
    """
    content: str              # 文本块内容
    tokens: int              # token数量
    chunk_order_index: int   # 在原文档中的顺序索引
    doc_id: str         # 原文档ID
    chunk_id: str            # 文本块唯一ID


class DocumentChunker:
    """
    文档分块器

    负责将原始文档分割成适当大小的文本块，支持多种分块策略：
    1. 基于token数量的智能分块
    2. 保持段落和句子完整性
    3. 支持块间重叠以保持上下文连续性
    """

    def __init__(
            self,
            chunk_token_size: int = 1200,
            chunk_overlap_token_size: int = 100,
            tiktoken_model_name: str = "gpt-4o"
    ):
        """
        初始化文档分块器

        Args:
            chunk_token_size: 每个块的目标token数量，默认1200
            chunk_overlap_token_size: 块之间的重叠token数量，默认100
            tiktoken_model_name: 用于token计算的模型名称，默认gpt-4o
        """
        self.chunk_token_size = chunk_token_size
        self.chunk_overlap_token_size = chunk_overlap_token_size
        self.tiktoken_model_name = tiktoken_model_name

        logger.info(
            f"DocumentChunker initialized: chunk_size={chunk_token_size}, "
            f"overlap={chunk_overlap_token_size}"
        )

    def _split_text_by_sentences(self, text: str) -> List[str]:
        """
        按句子分割文本

        使用正则表达式识别句子边界，支持中英文标点符号
        """
        # 定义句子结束标记
        sentence_endings = r'[.!?。！？\n\r]+'
        sentences = re.split(sentence_endings, text)

        # 过滤空句子
        return [sentence.strip() for sentence in sentences if sentence.strip()]

    def _chunk_text_by_tokens(self, text: str) -> List[str]:
        """
        根据 token 数量分割文本

        采用贪心算法，尽可能在句子边界分割，保持语义完整性
        """
        sentences = self._split_text_by_sentences(text)
        if not sentences:
            return []

        chunks = []
        current_chunk = ""
        current_tokens = 0

        for sentence in sentences:
            sentence_tokens = len(encode_string_by_tiktoken(sentence, self.tiktoken_model_name))

            # 处理超长句子
            if sentence_tokens > self.chunk_token_size:
                # 保存当前块
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                    current_tokens = 0

                # 按词分割超长句子
                chunks.extend(self._split_long_sentence(sentence))
                continue

            # 检查是否可以添加到当前块
            if current_tokens + sentence_tokens <= self.chunk_token_size:
                current_chunk += sentence + " "
                current_tokens += sentence_tokens
            else:
                # 保存当前块，开始新块
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + " "
                current_tokens = sentence_tokens

        # 保存最后一个块
        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _split_long_sentence(self, sentence: str) -> List[str]:
        """
        分割超长句子

        按词分割，确保每个块不超过token限制
        """
        words = sentence.split()
        chunks = []
        current_chunk = ""
        current_tokens = 0

        for word in words:
            word_tokens = len(encode_string_by_tiktoken(word, self.tiktoken_model_name))

            if current_tokens + word_tokens <= self.chunk_token_size:
                current_chunk += word + " "
                current_tokens += word_tokens
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = word + " "
                current_tokens = word_tokens

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks

    def _add_overlap_to_chunks(self, chunks: List[str]) -> List[str]:
        """为文本块添加重叠部分"""
        if len(chunks) <= 1 or self.chunk_overlap_token_size <= 0:
            return chunks

        overlapped_chunks = []

        for i, chunk in enumerate(chunks):
            if i == 0:
                # 第一个块不需要前置重叠
                overlapped_chunks.append(chunk)
                continue

            # 从前一个块获取重叠内容
            prev_chunk = chunks[i - 1]
            prev_tokens = encode_string_by_tiktoken(prev_chunk, self.tiktoken_model_name)

            # 计算重叠部分
            if len(prev_tokens) > self.chunk_overlap_token_size:
                overlap_tokens = prev_tokens[-self.chunk_overlap_token_size:]
                overlap_text = decode_tokens_by_tiktoken(overlap_tokens, self.tiktoken_model_name)
            else:
                overlap_text = prev_chunk

            # 合并重叠内容和当前块
            overlapped_chunk = overlap_text + " " + chunk
            overlapped_chunks.append(overlapped_chunk)

        return overlapped_chunks

    async def chunk_document(
            self,
            content: str,
            doc_id: Optional[str] = None
    ) -> List[DocumentChunk]:
        """
        将文档分块
        
        Args:
            content: 文档内容
            doc_id: 文档 ID，如果为 None 则自动生成
            
        Returns:
            文档块列表
        """
        if not content or not content.strip():
            logger.warning("Empty content provided for chunking")
            return []

        # 生成文档 ID
        if doc_id is None:
            doc_id = compute_mdhash_id(content, prefix="doc-")

        logger.info(f"Chunking document {doc_id}, content length: {len(content)}")

        # 分割文本
        chunks = self._chunk_text_by_tokens(content)

        # 添加重叠
        if self.chunk_overlap_token_size > 0:
            chunks = self._add_overlap_to_chunks(chunks)

        # 创建 DocumentChunk 对象
        document_chunks = []
        for i, chunk_content in enumerate(chunks):
            chunk_tokens = len(encode_string_by_tiktoken(chunk_content, self.tiktoken_model_name))
            chunk_id = compute_mdhash_id(f"{doc_id}_{i}", prefix="chunk-")

            document_chunk = DocumentChunk(
                content=chunk_content.strip(),
                tokens=chunk_tokens,
                chunk_order_index=i,
                doc_id=doc_id,
                chunk_id=chunk_id
            )
            document_chunks.append(document_chunk)

        logger.info(f"Document {doc_id} split into {len(document_chunks)} chunks")
        return document_chunks

    async def chunk_documents_batch(
            self,
            mixrag_documents: List[Dict[str, str]]
    ) -> List[List[DocumentChunk]]:
        """
        批量分块多个文档
        
        Args:
            mixrag_documents: 文档列表，每个文档包含 'content' 和可选的 'doc_id'
            
        Returns:
            每个文档的块列表
        """
        tasks = []
        for doc in mixrag_documents:
            content = doc.get('content', '')
            doc_id = doc.get('doc_id')
            tasks.append(self.chunk_document(content, doc_id))

        results = await asyncio.gather(*tasks)
        return results


# 兼容性函数
async def chunking_by_token_size(
        content: str,
        chunk_token_size: int = 1200,
        chunk_overlap_token_size: int = 100,
        tiktoken_model_name: str = "gpt-4o",
        doc_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    按 token 大小分块文档（兼容性函数）
    
    Args:
        content: 文档内容
        chunk_token_size: 块大小
        chunk_overlap_token_size: 重叠大小
        tiktoken_model_name: 模型名称
        doc_id: 文档 ID
        
    Returns:
        分块结果字典列表
    """
    chunker = DocumentChunker(
        chunk_token_size=chunk_token_size,
        chunk_overlap_token_size=chunk_overlap_token_size,
        tiktoken_model_name=tiktoken_model_name
    )

    chunks = await chunker.chunk_document(content, doc_id)

    # 转换为字典格式
    result = []
    for chunk in chunks:
        result.append({
            "content": chunk.content,
            "tokens": chunk.tokens,
            "chunk_order_index": chunk.chunk_order_index,
            "doc_id": chunk.doc_id,
            "chunk_id": chunk.chunk_id
        })

    return result


def validate_chunk_size(chunk_token_size: int, model_max_tokens: int = 16384) -> int:
    """
    验证并调整块大小
    
    Args:
        chunk_token_size: 期望的块大小
        model_max_tokens: 模型最大 token 限制
        
    Returns:
        调整后的块大小
    """
    # 确保块大小不超过模型限制的一半（为提示词等预留空间）
    max_chunk_size = model_max_tokens // 2

    if chunk_token_size > max_chunk_size:
        logger.warning(
            f"Chunk size {chunk_token_size} exceeds recommended maximum {max_chunk_size}, "
            f"adjusting to {max_chunk_size}"
        )
        return max_chunk_size

    if chunk_token_size < 100:
        logger.warning(f"Chunk size {chunk_token_size} is too small, setting to 100")
        return 100

    return chunk_token_size
