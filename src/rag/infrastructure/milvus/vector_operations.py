"""
Milvus 向量操作管理器

提供向量数据的 CRUD 操作和相似性搜索功能
"""

import asyncio
from typing import Any, Dict, List, Optional

from pymilvus import Collection

from src.config import config
from src.rag.llm.clients import call_embedding
from src.rag.tools.logger import logger


class MilvusVectorOperations:
    """Milvus 向量操作管理器 - 单例模式"""

    _lock = asyncio.Lock()

    def __init__(self):
        self.embedding_func = call_embedding

        # 从统一配置获取Milvus配置
        storage_config = config.storage

        # 嵌入维度配置
        self.embedding_dim = storage_config.milvus_dimension

        # 索引配置
        self.index_type = "HNSW"
        self.metric_type = "COSINE"
        self.cosine_better_than_threshold = 0.2

        # 确保meta_fields是有序的，避免字段顺序错位
        self.meta_fields = sorted(list(set(['doc_id', 'chunk_order_index', 'tokens', 'content', 'description', 'keywords'])))

        # 集合名称配置
        self.collection_name = storage_config.milvus_collection

        # 字段配置
        self.id_field = "id"
        self.vector_field = "vector"
        # 移除text_field，只使用content字段（在meta_fields中定义）

        # 连接池引用
        self._connection_pool = None

        logger.info(f"Milvus vector operations initialized: collection={self.collection_name}")
        logger.info(f"Meta fields configured: {self.meta_fields}")

    def set_connection_pool(self, connection_pool):
        """设置连接池引用"""
        self._connection_pool = connection_pool

    async def upsert(self, data: Dict[str, Dict[str, Any]]) -> None:
        """
        插入或更新向量数据

        Args:
            data: 要插入的数据字典
        """
        # 准备数据
        entities = await self._prepare_entities(data)

        # 使用连接池执行操作
        async with self._connection_pool.get_connection() as connection_alias:
            collection = Collection(name=self.collection_name, using=connection_alias)

            # 执行插入
            await asyncio.get_event_loop().run_in_executor(
                None, collection.upsert, entities
            )

            # 刷新数据到磁盘，确保数据可见
            await asyncio.get_event_loop().run_in_executor(
                None, collection.flush
            )

        logger.info(f"Successfully upserted {len(data)} vectors to collection: {self.collection_name}")

    async def _prepare_entities(self, data: Dict[str, Dict[str, Any]]) -> List[List]:
        """准备插入实体数据"""
        ids = []
        vectors = []
        meta_data = {field: [] for field in self.meta_fields}

        for doc_id, doc_data in data.items():
            ids.append(doc_id)

            # 获取或生成向量
            if "vector" in doc_data and doc_data["vector"]:
                vectors.append(doc_data["vector"])
            else:
                # 使用嵌入函数生成向量
                text_content = doc_data.get("content", "")
                if text_content and self.embedding_func:
                    # 检查embedding_func是否是异步函数
                    if asyncio.iscoroutinefunction(self.embedding_func):
                        # 异步embedding函数
                        embeddings = await self.embedding_func([text_content])
                        vector = embeddings[0] if embeddings else [0.0] * self.embedding_dim
                    else:
                        # 同步embedding函数
                        vector = await asyncio.get_event_loop().run_in_executor(
                            None, self.embedding_func, text_content
                        )
                    vectors.append(vector)
                else:
                    # 使用零向量作为占位符
                    vectors.append([0.0] * self.embedding_dim)

            # 元数据字段
            for field in self.meta_fields:
                if field in doc_data:
                    value = doc_data[field]
                    # 确保字符串字段不超过长度限制
                    if isinstance(value, str):
                        value = value[:65535]
                    meta_data[field].append(value)
                else:
                    # 提供默认值
                    if field == 'doc_id':
                        meta_data[field].append(doc_id.split('_')[0] if '_' in doc_id else doc_id)
                    elif field == 'chunk_order_index':
                        meta_data[field].append(0)
                    elif field == 'tokens':
                        meta_data[field].append(0)
                    elif field == 'content':
                        # 如果没有content字段，使用空字符串
                        meta_data[field].append("")
                    else:
                        meta_data[field].append("")

        # 构建实体列表 - 移除texts字段，只保留ids, vectors和meta_fields
        entities = [ids, vectors]
        for field in self.meta_fields:
            entities.append(meta_data[field])

        return entities

    async def query_by_vector(
            self,
            query_vector: List[float],
            top_k: int = 5,
            output_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        向量相似性查询

        Args:
            query_vector: 查询向量
            top_k: 返回结果数量
            output_fields: 输出字段列表

        Returns:
            相似性搜索结果
        """
        # 设置输出字段
        if output_fields is None:
            output_fields = [self.id_field] + list(self.meta_fields)

        # 搜索参数
        search_params = {
            "metric_type": self.metric_type,
            "params": {"ef": min(top_k * 2, 200)} if self.index_type == "HNSW" else {}
        }

        # 使用连接池执行搜索
        async with self._connection_pool.get_connection() as connection_alias:
            collection = Collection(name=self.collection_name, using=connection_alias)

            # 执行搜索
            def search_sync():
                return collection.search(
                    data=[query_vector],  # 查询向量列表
                    anns_field=self.vector_field,  # 向量字段名
                    param=search_params,  # 搜索参数
                    limit=top_k,  # 返回数量限制
                    expr=None,  # 过滤表达式（暂时不用）
                    output_fields=output_fields  # 输出字段列表
                )

            results = await asyncio.get_event_loop().run_in_executor(
                None, search_sync
            )

        # 处理搜索结果
        formatted_results = []
        for hits in results:
            for hit in hits:
                # 处理距离和分数的精度问题
                distance = hit.distance

                # 初始化结果字典
                result = {
                    self.id_field: hit.id,
                    "distance": distance
                }

                # 添加实体字段
                if hasattr(hit, 'entity'):
                    for field in output_fields:
                        if field != self.id_field:  # id已经添加过了
                            result[field] = hit.entity.get(field)

                formatted_results.append(result)

        logger.debug(f"Vector search returned {len(formatted_results)} results")
        return formatted_results

    async def query_by_text(
            self,
            query_text: str,
            top_k: int = 5,
            output_fields: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        文本查询（先转换为向量再搜索）

        Args:
            query_text: 查询文本
            top_k: 返回结果数量
            output_fields: 输出字段列表

        Returns:
            搜索结果
        """
        # 将文本转换为向量
        # 检查embedding_func是否是异步函数
        if asyncio.iscoroutinefunction(self.embedding_func):
            # 异步embedding函数
            embeddings = await self.embedding_func([query_text])
            query_vector = embeddings[0] if embeddings else []
        else:
            # 同步embedding函数
            query_vector = await asyncio.get_event_loop().run_in_executor(
                None, self.embedding_func, query_text
            )

        # 执行向量搜索
        return await self.query_by_vector(query_vector, top_k, output_fields)

    async def delete(self, ids: List[str]) -> None:
        """
        删除向量数据

        Args:
            ids: 要删除的向量ID列表
        """
        if not self._connection_pool or not ids:
            return

        # 构建删除表达式
        if len(ids) == 1:
            expr = f'{self.id_field} == "{ids[0]}"'
        else:
            id_list = '", "'.join(ids)
            expr = f'{self.id_field} in ["{id_list}"]'

        logger.debug(f"Delete expression: {expr}")

        # 使用连接池执行删除
        async with self._connection_pool.get_connection() as connection_alias:
            collection = Collection(name=self.collection_name, using=connection_alias)

            # 执行删除
            def delete_sync():
                return collection.delete(expr)

            result = await asyncio.get_event_loop().run_in_executor(
                None, delete_sync
            )

        logger.info(f"Successfully deleted {len(ids)} vectors from collection: {self.collection_name}")

    async def delete_by_doc_id(self, doc_id: str) -> None:
        """
        根据 doc_id 删除向量数据

        Args:
            doc_id: 要删除的文档 ID
        """
        expr = f'doc_id == "{doc_id}"'
        logger.debug(f"Delete expression: {expr}")

        # 使用连接池执行删除
        async with self._connection_pool.get_connection() as connection_alias:
            collection = Collection(name=self.collection_name, using=connection_alias)

            # 执行删除
            def delete_sync():
                return collection.delete(expr)

            result = await asyncio.get_event_loop().run_in_executor(
                None, delete_sync
            )

        # Milvus的delete不返回删除的数量，所以我们只记录操作
        logger.info(f"Delete operation for doc_id '{doc_id}' executed on collection: {self.collection_name}")

    async def upsert_vector(
            self,
            vector_id: str,
            vector: List[float],
            text: str,
    ) -> None:
        """
        插入或更新单个向量数据

        Args:
            vector_id: 向量ID
            vector: 向量数据
            text: 文本内容
        """
        # 构建数据字典格式，符合upsert方法的要求
        data = {
            vector_id: {
                "vector": vector,
                "content": text,
            }
        }

        # 调用现有的upsert方法
        await self.upsert(data)

        logger.debug(f"Successfully upserted vector: {vector_id}")

    # 兼容性方法
    async def query(self, query_text: str, top_k: int = 5, ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """兼容性查询方法"""
        return await self.query_by_text(query_text, top_k)

    async def index_done_callback(self) -> None:
        """索引完成回调 - Milvus 自动维护索引"""
        logger.debug(f"Index done callback for collection: {self.collection_name}")

    async def initialize(self) -> None:
        """初始化向量操作管理器"""
        if self._connection_pool is None:
            from .client import milvus_connection_pool
            self.set_connection_pool(milvus_connection_pool)
        logger.info(f"Milvus vector operations initialized: {self.collection_name}")

    async def finalize(self) -> None:
        """清理向量操作管理器"""
        # 向量操作管理器本身不需要特殊清理，连接池由client管理
        logger.info(f"Milvus vector operations finalized: {self.collection_name}")


# 创建单例实例
milvus_vector_operations = MilvusVectorOperations()

# 设置连接池引用（需要在初始化后设置）
def initialize_vector_operations():
    """初始化向量操作管理器"""
    from .client import milvus_connection_pool
    milvus_vector_operations.set_connection_pool(milvus_connection_pool)