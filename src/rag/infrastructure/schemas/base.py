"""
RAG 基础设施数据模型

定义 RAG 系统基础设施层的数据结构和接口
"""

import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Literal, Set, TypedDict, Optional


class TextChunkSchema(TypedDict):
    """文本块数据结构"""
    tokens: int
    content: str
    doc_id: str
    chunk_order_index: int


class DocStatus(Enum):
    """文档处理状态"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class DocProcessingStatus:
    """文档处理状态数据结构"""
    content: str
    content_summary: str
    content_length: int
    file_path: str
    status: str
    created_at: str
    updated_at: str
    chunks_count: Optional[int] = None
    error: Optional[str] = None


@dataclass
class QueryParam:
    """查询参数配置"""

    mode: Literal["global", "naive", "mix"] = "global"
    """检索模式"""

    only_need_context: bool = False
    """是否只需要返回上下文"""

    only_need_prompt: bool = False
    """是否只需要返回提示词"""

    response_type: str = "Multiple Paragraphs"
    """响应格式类型"""

    stream: bool = False
    """是否启用流式输出"""

    top_k: int = int(os.getenv("TOP_K", "60"))
    """检索结果数量"""

    max_token_for_text_unit: int = int(os.getenv("MAX_TOKEN_TEXT_CHUNK", "4000"))
    """文本单元最大 token 数"""

    max_token_for_global_context: int = int(os.getenv("MAX_TOKEN_RELATION_DESC", "4000"))
    """全局上下文最大 token 数"""

    max_token_for_local_context: int = int(os.getenv("MAX_TOKEN_ENTITY_DESC", "4000"))
    """本地上下文最大 token 数"""

    hl_keywords: List[str] = field(default_factory=list)
    """高级关键词列表"""

    ll_keywords: List[str] = field(default_factory=list)
    """低级关键词列表"""

    conversation_history: List[Dict[str, str]] = field(default_factory=list)
    """对话历史"""

    history_turns: int = 3
    """历史轮次数"""

    ids: Optional[List[str]] = None
    """ID 过滤列表"""


@dataclass
class BaseStorage(ABC):
    """存储命名空间基类"""

    async def initialize(self):
        """初始化存储"""
        pass

    async def finalize(self):
        """清理存储"""
        pass

    @abstractmethod
    async def index_done_callback(self) -> None:
        """索引完成回调"""
        pass

    @abstractmethod
    async def drop(self) -> Dict[str, str]:
        """删除所有数据"""
        pass


@dataclass
class BaseKVBaseStorage(BaseStorage, ABC):
    """键值存储基类"""

    embedding_func: Any  # EmbeddingFunc 类型，避免循环导入

    @abstractmethod
    async def get_by_id(self, id: str) -> Optional[Dict[str, Any]]:
        """根据 ID 获取值"""
        pass

    @abstractmethod
    async def get_by_ids(self, ids: List[str]) -> List[Dict[str, Any]]:
        """根据 ID 列表获取值"""
        pass

    @abstractmethod
    async def filter_keys(self, keys: Set[str]) -> Set[str]:
        """过滤不存在的键"""
        pass

    @abstractmethod
    async def upsert(self, data: Dict[str, Dict[str, Any]]) -> None:
        """插入或更新数据"""
        pass

    @abstractmethod
    async def delete(self, ids: List[str]) -> None:
        """删除指定 ID 的数据"""
        pass

    async def drop_cache_by_modes(self, modes: Optional[List[str]] = None) -> bool:
        """
        根据模式删除缓存

        Args:
            modes: 要删除的缓存模式列表

        Returns:
            删除是否成功
        """
        # 基类默认实现，子类可以重写
        return True


@dataclass
class BaseVectorBaseStorage(BaseStorage, ABC):
    """向量存储基类"""

    embedding_func: Any  # EmbeddingFunc 类型，避免循环导入
    meta_fields: Set[str] = field(default_factory=set)

    @abstractmethod
    async def upsert(self, data: Dict[str, Dict[str, Any]]) -> None:
        """插入或更新向量数据"""
        pass

    @abstractmethod
    async def query(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """向量查询"""
        pass

    @abstractmethod
    async def delete(self, ids: List[str]) -> None:
        """删除向量数据"""
        pass


@dataclass
class BaseGraphBaseStorage(BaseStorage, ABC):
    """图存储基类"""

    @abstractmethod
    async def has_node(self, node_id: str) -> bool:
        """检查节点是否存在"""
        pass

    @abstractmethod
    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool:
        """检查边是否存在"""
        pass

    @abstractmethod
    async def get_node(self, node_id: str) -> Optional[Dict[str, Any]]:
        """获取节点"""
        pass

    @abstractmethod
    async def node_degree(self, node_id: str) -> int:
        """获取节点度数"""
        pass

    @abstractmethod
    async def edge_degree(self, src_id: str, tgt_id: str) -> int:
        """获取边度数"""
        pass

    @abstractmethod
    async def get_edge(self, source_node_id: str, target_node_id: str) -> Optional[Dict[str, Any]]:
        """获取边"""
        pass

    @abstractmethod
    async def get_node_edges(self, source_node_id: str) -> List[Dict[str, Any]]:
        """获取节点的所有边"""
        pass

    @abstractmethod
    async def upsert_node(self, node_id: str, node_data: Dict[str, Any]) -> None:
        """插入或更新节点"""
        pass

    @abstractmethod
    async def upsert_edge(
        self, source_node_id: str, target_node_id: str, edge_data: Dict[str, Any]
    ) -> None:
        """插入或更新边"""
        pass

    @abstractmethod
    async def delete_node(self, node_id: str) -> None:
        """删除节点"""
        pass

    @abstractmethod
    async def delete_edge(self, source_node_id: str, target_node_id: str) -> None:
        """删除边"""
        pass
