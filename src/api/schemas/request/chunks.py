"""
Chunk 搜索请求相关的 API 模式定义
"""

from typing import Optional

from pydantic import BaseModel, Field


class ChunkSimilaritySearchRequest(BaseModel):
    """相似度搜索请求"""
    query: str = Field(..., description="搜索查询")
    top_k: int = Field(10, ge=1, le=100, description="返回结果数量")
    doc_id: Optional[str] = Field(None, description="限制在特定文档")
    kb_id: Optional[str] = Field(None, description="限制在特定知识库")
    threshold: Optional[float] = Field(0.2, ge=0, le=1, description="相似度阈值")


class ChunkKeywordSearchRequest(BaseModel):
    """关键词搜索请求"""
    keywords: str = Field(..., description="关键词")
    doc_id: Optional[str] = Field(None, description="限制在特定文档")
    kb_id: Optional[str] = Field(None, description="限制在特定知识库")
    limit: int = Field(10, ge=1, le=100, description="返回结果数量")
    offset: int = Field(0, ge=0, description="偏移量")
