"""
Chunk 管理相关的 API 模式定义
"""

from datetime import datetime
from typing import Optional, Dict, Any

from pydantic import BaseModel, Field


class ChunkInfo(BaseModel):
    """Chunk信息"""
    chunk_id: str = Field(..., description="Chunk唯一标识")
    doc_id: str = Field(..., description="所属文档ID")
    content: str = Field(..., description="Chunk内容")
    chunk_index: int = Field(..., description="Chunk索引")
    chunk_size: int = Field(..., description="Chunk大小")
    start_pos: Optional[int] = Field(None, description="在文档中的起始位置")
    end_pos: Optional[int] = Field(None, description="在文档中的结束位置")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    @staticmethod
    def create(chunk_id, chunk_data):
        # 兼容不同的字段名格式
        doc_id = chunk_data.get("doc_id") or chunk_data.get("doc_id", "")
        content = chunk_data.get("content", "")
        chunk_index = chunk_data.get("order_index") or chunk_data.get("chunk_order_index", 0)

        return ChunkInfo(
            chunk_id=chunk_id,
            doc_id=doc_id,
            content=content,
            chunk_index=chunk_index,
            chunk_size=len(content),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )


class ChunkSearchResult(BaseModel):
    """Chunk搜索结果"""
    chunk_id: str = Field(..., description="Chunk唯一标识")
    doc_id: str = Field(..., description="所属文档ID")
    content: str = Field(..., description="Chunk内容")
    chunk_index: int = Field(..., description="Chunk索引")
    similarity: Optional[float] = Field(None, description="相似度分数")
    distance: Optional[float] = Field(None, description="距离分数")
    created_at: datetime = Field(..., description="创建时间")
