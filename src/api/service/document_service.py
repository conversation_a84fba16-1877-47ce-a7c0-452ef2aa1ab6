"""
文档处理服务 - 业务逻辑层

负责处理所有文档相关的业务逻辑，调用RAG层的基础功能
"""

# 对于二进制文件，返回base64编码
import base64
import hashlib
import logging
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List

import magic
from fastapi import UploadFile
from fastapi.responses import StreamingResponse

from src.api.schemas.models.base import create_paginated_response
from src.api.schemas.models.documents import DocumentInfo, ProcessStatus, UploadStatus
from src.api.schemas.request.pipelines import DocumentPipelineRequest
from src.api.schemas.response.documents import DocumentListResponse, DocumentStatsResponse
from src.api.schemas.response.pipelines import CreatePipelineResponse
from src.api.schemas.response.responses import DocumentUploadResponse
from src.rag.infrastructure.db.client import postgre_sql_client
from src.rag.infrastructure.db.document_operations import document_operations
from src.rag.infrastructure.minio.client import min_io_client
from src.rag.infrastructure.minio.object_operations import object_operations
from src.rag.infrastructure.milvus import milvus_vector_operations
from src.rag.infrastructure import neo4j_connection_manager
# 直接导入数据库模块
from src.rag.infrastructure.db.pipeline_operations import pipeline_operations
from src.rag.pipeline.pipeline_manager import pipeline_manager

from src.rag.tools import logger


class DocumentService:
    """文档处理服务 - 业务逻辑层"""

    def __init__(self):
        """初始化文档服务"""
        self._initialized = False

        # 支持的文件类型
        self.ALLOWED_MIME_TYPES = {
            'text/plain': '.txt',
            'application/pdf': '.pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
            'text/markdown': '.md',
            'application/msword': '.doc'
        }

        # 最大文件大小 (50MB)
        self.MAX_FILE_SIZE = 50 * 1024 * 1024

    def _validate_file(self, file: UploadFile) -> None:
        """验证上传文件"""
        if not file.filename:
            raise ValueError("文件名不能为空")

        # 检查文件大小
        if hasattr(file, 'size') and file.size > self.MAX_FILE_SIZE:
            raise ValueError(f"文件大小超过限制 ({self.MAX_FILE_SIZE / 1024 / 1024:.1f}MB)")

    def _detect_mime_type(self, file_content: bytes, filename: str) -> str:
        """检测文件MIME类型"""
        # 使用 python-magic 检测文件类型
        mime_type = magic.from_buffer(file_content, mime=True)

        # 特殊处理一些文件类型
        if filename.lower().endswith('.md'):
            return 'text/markdown'
        elif filename.lower().endswith('.txt'):
            return 'text/plain'

        return mime_type

    def _generate_doc_id(self, content_hash: str) -> str:
        """生成文档ID"""
        timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
        return f"doc_{timestamp}_{content_hash[:8]}"

    async def upload_single_file(
            self,
            file: UploadFile,
            kb_id: str,
            auto_process: bool = True,
            uploaded_by: Optional[str] = None
    ) -> DocumentUploadResponse:
        """上传单个文件 - 业务逻辑"""
        # 验证文件
        self._validate_file(file)

        # 读取文件内容
        file_content = await file.read()
        file_size = len(file_content)

        # 检测文件类型
        mime_type = self._detect_mime_type(file_content, file.filename)

        if mime_type not in self.ALLOWED_MIME_TYPES:
            raise ValueError(f"不支持的文件类型: {mime_type}")

        # 计算文件哈希
        content_hash = hashlib.md5(file_content).hexdigest()

        # 生成文档ID
        doc_id = self._generate_doc_id(content_hash)

        # 检查文档是否已存在（在同一知识库中）
        existing_doc = await document_operations.get_document_by_hash_and_kb(content_hash, kb_id)
        if existing_doc:
            return DocumentUploadResponse(
                doc_id=existing_doc['doc_id'],
                filename=file.filename,
                file_size=file_size,
                upload_url=None,
                message="文档已存在，跳过上传"
            )

        # 上传到MinIO
        object_key = f"{doc_id}/{file.filename}"
        await object_operations.upload_bytes(
            object_key=object_key,
            data=file_content,
            content_type=mime_type
        )

        # 保存文档元数据到数据库
        doc_metadata = {
            'doc_id': doc_id,
            'original_filename': file.filename,
            'file_extension': file.filename.split('.')[-1] if '.' in file.filename else None,
            'file_size': file_size,
            'mime_type': mime_type,
            'content_hash': content_hash,
            'minio_bucket': min_io_client.bucket_name,
            'minio_object_key': object_key,
            'upload_status': UploadStatus.UPLOADED.value,
            'process_status': ProcessStatus.PENDING.value,
            'created_at': datetime.now(timezone.utc).replace(tzinfo=None),
            'updated_at': datetime.now(timezone.utc).replace(tzinfo=None),
            'uploaded_by': uploaded_by,
            'kb_id': kb_id,
            'chunks_count': 0,
            'entities_count': 0,
            'relationships_count': 0,
            'error_message': None
        }

        await document_operations.save_document_metadata(doc_metadata)

        # 根据auto_process参数决定是否自动处理
        task_id = None
        process_status = ProcessStatus.PENDING

        if auto_process:
            # 提交处理任务
            task_id = await pipeline_manager.submit_document_processing_chain(
                doc_id=doc_id
            )
            process_status = ProcessStatus.PROCESSING
            # 更新处理状态
            await document_operations.update_document_status(
                doc_id=doc_id,
                process_status=process_status.value,
                task_id=task_id
            )

        return DocumentUploadResponse(
            doc_id=doc_id,
            filename=file.filename,
            file_size=file_size,
            upload_url=None,
            message="文档上传成功" + (" 并已开始处理" if auto_process else "")
        )

    async def upload_multiple_files(
            self,
            files: List[UploadFile],
            kb_id: str,
            auto_process: bool = True
    ) -> List[DocumentUploadResponse]:
        """批量上传文件 - 业务逻辑"""
        results = []
        for file in files:
            result = await self.upload_single_file(file, kb_id=kb_id, auto_process=auto_process)
            results.append(result)
        return results

    async def list_documents(
            self,
            page: int = 1,
            page_size: int = 20,
            upload_status: Optional[str] = None,
            process_status: Optional[str] = None,
            filename_filter: Optional[str] = None,
            kb_id: Optional[str] = None,
            sort_by: str = "created_at",
            sort_order: str = "desc"
    ) -> DocumentListResponse:
        """获取文档列表 - 业务逻辑"""

        # 构建查询条件
        filters = {}
        if upload_status:
            filters['upload_status'] = upload_status
        if process_status:
            filters['process_status'] = process_status
        if filename_filter:
            filters['filename_like'] = f"%{filename_filter}%"
        if kb_id:
            filters['kb_id'] = kb_id

        # 查询文档列表
        result = await document_operations.list_documents(
            page=page,
            page_size=page_size,
            filters=filters,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 转换为响应格式
        documents = []
        for doc in result['documents']:
            # 从 minio_object_key 中提取文件扩展名
            file_extension = None
            minio_object_key = doc.get('minio_object_key', '')
            if minio_object_key and '.' in minio_object_key:
                file_extension = minio_object_key.split('.')[-1]

            document_info = DocumentInfo(
                doc_id=doc['doc_id'],
                original_filename=doc['original_filename'],
                file_extension=file_extension,
                file_size=doc['file_size'],
                mime_type=doc['mime_type'],
                minio_bucket=doc['minio_bucket'],
                minio_object_key=doc['minio_object_key'],
                content_hash=doc.get('content_hash'),
                upload_status=UploadStatus(doc['upload_status']),
                process_status=ProcessStatus(doc['process_status']),
                created_at=doc['created_at'],
                updated_at=doc['updated_at'],
                uploaded_by=doc.get('uploaded_by'),
                error_message=doc.get('error_message'),
                chunks_count=doc.get('chunks_count', 0),
                entities_count=doc.get('entities_count', 0),
                relationships_count=doc.get('relationships_count', 0),
                kb_id=doc.get('kb_id')
            )
            documents.append(document_info)

        return create_paginated_response(
            records=documents,
            page=page,
            page_size=page_size,
            total=result['total']
        )

    async def get_document_stats(self) -> DocumentStatsResponse:
        """获取文档统计信息 - 业务逻辑"""
        stats = await document_operations.get_document_stats()
        return DocumentStatsResponse(
            total_documents=stats.get('total_documents', 0),
            processing_count=stats.get('processing_count', 0),
            storage_size_bytes=stats.get('storage_size_bytes', 0),
            avg_file_size_bytes=stats.get('avg_file_size_bytes', None)
        )

    async def get_document_info(self, doc_id: str) -> DocumentInfo:
        """获取文档信息 - 业务逻辑"""
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            raise ValueError("文档不存在")
        # 从 minio_object_key 中提取文件扩展名
        file_extension = None
        minio_object_key = doc_metadata.get('minio_object_key', '')
        if minio_object_key and '.' in minio_object_key:
            file_extension = minio_object_key.split('.')[-1]
        return DocumentInfo(
            doc_id=doc_metadata['doc_id'],
            original_filename=doc_metadata['original_filename'],
            file_extension=file_extension,
            file_size=doc_metadata['file_size'],
            mime_type=doc_metadata['mime_type'],
            minio_bucket=doc_metadata['minio_bucket'],
            minio_object_key=doc_metadata['minio_object_key'],
            content_hash=doc_metadata.get('content_hash'),
            upload_status=UploadStatus(doc_metadata['upload_status']),
            process_status=ProcessStatus(doc_metadata['process_status']),
            created_at=doc_metadata['created_at'],
            updated_at=doc_metadata['updated_at'],
            uploaded_by=doc_metadata.get('uploaded_by'),
            error_message=doc_metadata.get('error_message'),
            chunks_count=doc_metadata.get('chunks_count', 0),
            entities_count=doc_metadata.get('entities_count', 0),
            relationships_count=doc_metadata.get('relationships_count', 0)
        )

    async def get_document_processing_stats(self, doc_id: str) -> Dict[str, Any]:
        """获取文档处理统计信息 - 业务逻辑"""
        # 获取文档基本信息
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            raise ValueError("文档不存在")
        # 获取相关的流水线信息
        pipeline_id = doc_metadata.get('pipeline_id')
        pipeline_stats = {}
        if pipeline_id:
            pipeline_status = await pipeline_manager.get_pipeline_status(pipeline_id)
            if pipeline_status:
                pipeline_stats = {
                    "pipeline_id": pipeline_id,
                    "pipeline_status": pipeline_status.get("status"),
                    "total_tasks": pipeline_status.get("total_tasks", 0),
                    "completed_tasks": pipeline_status.get("completed_tasks", 0),
                    "failed_tasks": pipeline_status.get("failed_tasks", 0),
                    "running_tasks": pipeline_status.get("running_tasks", 0),
                    "created_at": pipeline_status.get("created_at"),
                    "started_at": pipeline_status.get("started_at"),
                    "completed_at": pipeline_status.get("completed_at")
                }

        # 获取分块统计
        chunks_stats = {
            "total_chunks": doc_metadata.get('chunks_count', 0)
        }

        # 获取实体和关系统计
        graph_stats = {
            "entities_count": doc_metadata.get('entities_count', 0),
            "relationships_count": doc_metadata.get('relationships_count', 0)
        }

        return {
            "doc_id": doc_id,
            "processing_stats": pipeline_stats,
            "chunks_stats": chunks_stats,
            "graph_stats": graph_stats,
            "last_updated": doc_metadata.get('updated_at')
        }

    async def download_document(self, doc_id: str) -> StreamingResponse:
        """下载文档 - 业务逻辑"""

        # 获取文档元数据
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            raise ValueError("文档不存在")

        # 从MinIO获取文件流
        file_stream = await object_operations.get_object_stream(
            doc_metadata['minio_object_key']
        )

        # 返回流式响应
        return StreamingResponse(
            file_stream,
            media_type=doc_metadata['mime_type'],
            headers={
                "Content-Disposition": f"attachment; filename=\"{doc_metadata['original_filename']}\""
            }
        )

    async def get_document_content(self, doc_id: str) -> Dict[str, Any]:
        """获取文档内容 - 业务逻辑"""

        # 获取文档元数据
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            raise ValueError("文档不存在")

        # 从MinIO获取文件内容
        content_bytes = await object_operations.get_object_bytes(
            doc_metadata['minio_object_key']
        )

        # 根据文件类型处理内容
        if doc_metadata['mime_type'] == 'text/plain':
            content = content_bytes.decode('utf-8')
        elif doc_metadata['mime_type'] == 'text/markdown':
            content = content_bytes.decode('utf-8')
        else:
            content = base64.b64encode(content_bytes).decode('utf-8')

        return {
            "doc_id": doc_id,
            "filename": doc_metadata['original_filename'],
            "mime_type": doc_metadata['mime_type'],
            "content": content,
            "file_size": doc_metadata['file_size']
        }

    async def delete_document(self, doc_id: str) -> Dict[str, Any]:
        """删除文档 - 业务逻辑（综合删除所有相关数据）"""
        logger.info(f"🗑️ 开始删除文档: {doc_id}")

        # 初始化删除结果
        deletion_results = {
            "postgresql": {"success": False, "message": "", "details": {}},
            "minio": {"success": False, "message": "", "details": {}},
            "neo4j": {"success": False, "message": "", "details": {}},
            "milvus": {"success": False, "message": "", "details": {}},
            "redis": {"success": False, "message": "", "details": {}},
            "pipeline": {"success": False, "message": "", "details": {}}
        }

        successful_systems = []
        failed_systems = []

        # 1. 首先获取文档元数据（用于后续删除操作）
        doc_metadata = await document_operations.get_document_metadata(doc_id)
        if not doc_metadata:
            return {
                "doc_id": doc_id,
                "overall_success": False,
                "message": f"文档 {doc_id} 不存在",
                "deletion_details": deletion_results,
                "successful_systems": 0,
                "total_systems": 5,
                "success_rate": "0%",
                "deleted_from": [],
                "failed_systems": ["文档不存在"]
            }

        # 2. 删除相关的流水线和任务
        try:
            await self._delete_document_pipelines(doc_id)
            deletion_results["pipeline"] = {
                "success": True,
                "message": "流水线删除成功",
                "details": {"doc_id": doc_id}
            }
            successful_systems.append("Pipeline")
            logger.info(f"✅ 流水线删除成功: {doc_id}")
        except Exception as e:
            logger.error(f"❌ 流水线删除失败: {e}", exc_info=True)
            deletion_results["pipeline"] = {
                "success": False,
                "message": f"流水线删除失败: {e}",
                "details": {"error": str(e)}
            }
            failed_systems.append("Pipeline")

        # 3. 删除Neo4j中的图数据
        try:
            await self._delete_document_from_neo4j(doc_id)
            deletion_results["neo4j"] = {
                "success": True,
                "message": "Neo4j数据删除成功",
                "details": {"doc_id": doc_id}
            }
            successful_systems.append("Neo4j")
            logger.info(f"✅ Neo4j数据删除成功: {doc_id}")
        except Exception as e:
            logger.error(f"❌ Neo4j数据删除失败: {e}", exc_info=True)
            deletion_results["neo4j"] = {
                "success": False,
                "message": f"Neo4j数据删除失败: {e}",
                "details": {"error": str(e)}
            }
            failed_systems.append("Neo4j")

        # 4. 删除Milvus中的向量数据
        try:
            await self._delete_document_from_milvus(doc_id)
            deletion_results["milvus"] = {
                "success": True,
                "message": "Milvus数据删除成功",
                "details": {"doc_id": doc_id}
            }
            successful_systems.append("Milvus")
            logger.info(f"✅ Milvus数据删除成功: {doc_id}")
        except Exception as e:
            logger.error(f"❌ Milvus数据删除失败: {e}", exc_info=True)
            deletion_results["milvus"] = {
                "success": False,
                "message": f"Milvus数据删除失败: {e}",
                "details": {"error": str(e)}
            }
            failed_systems.append("Milvus")

        # 5. 删除Redis中的chunk数据
        try:
            await self._delete_document_from_redis(doc_id)
            deletion_results["redis"] = {
                "success": True,
                "message": "Redis chunk数据删除成功",
                "details": {"doc_id": doc_id}
            }
            successful_systems.append("Redis")
            logger.info(f"✅ Redis chunk数据删除成功: {doc_id}")
        except Exception as e:
            logger.error(f"❌ Redis chunk数据删除失败: {e}", exc_info=True)
            deletion_results["redis"] = {
                "success": False,
                "message": f"Redis chunk数据删除失败: {e}",
                "details": {"error": str(e)}
            }
            failed_systems.append("Redis")

        # 6. 删除MinIO中的文件
        if doc_metadata and doc_metadata.get('minio_object_key'):
            try:
                await object_operations.delete_file(doc_metadata['minio_object_key'])
                deletion_results["minio"] = {
                    "success": True,
                    "message": "MinIO文件删除成功",
                    "details": {"object_key": doc_metadata['minio_object_key']}
                }
                successful_systems.append("MinIO")
                logger.info(f"✅ MinIO文件删除成功: {doc_metadata['minio_object_key']}")
            except Exception as e:
                logger.error(f"❌ MinIO文件删除失败: {e}", exc_info=True)
                deletion_results["minio"] = {
                    "success": False,
                    "message": f"MinIO文件删除失败: {e}",
                    "details": {"error": str(e)}
                }
                failed_systems.append("MinIO")
        else:
            deletion_results["minio"] = {
                "success": True,
                "message": "无MinIO文件需要删除",
                "details": {"reason": "no_object_key"}
            }
            successful_systems.append("MinIO")

        # 7. 最后删除PostgreSQL中的文档记录
        success = await document_operations.delete_document(doc_id)
        if success:
            deletion_results["postgresql"] = {
                "success": True,
                "message": "PostgreSQL记录删除成功",
                "details": {"doc_id": doc_id}
            }
            successful_systems.append("PostgreSQL")
            logger.info(f"✅ PostgreSQL记录删除成功: {doc_id}")
        else:
            deletion_results["postgresql"] = {
                "success": False,
                "message": "PostgreSQL记录删除失败：文档不存在",
                "details": {"reason": "document_not_found"}
            }
            failed_systems.append("PostgreSQL")

        # 计算成功率
        total_systems = len(deletion_results)
        successful_count = len(successful_systems)
        success_rate = f"{(successful_count / total_systems * 100):.1f}%"
        overall_success = successful_count >= 3  # 至少3个系统删除成功才算整体成功

        result = {
            "doc_id": doc_id,
            "overall_success": overall_success,
            "message": f"文档删除完成，成功率: {success_rate} ({successful_count}/{total_systems})",
            "deletion_details": deletion_results,
            "successful_systems": successful_count,
            "total_systems": total_systems,
            "success_rate": success_rate,
            "deleted_from": successful_systems,
            "failed_systems": failed_systems
        }

        if overall_success:
            logger.info(f"🎉 文档删除成功: {doc_id}, 成功率: {success_rate}")
        else:
            logger.warning(f"⚠️ 文档删除部分失败: {doc_id}, 成功率: {success_rate}")

        return result

    async def _delete_document_pipelines(self, doc_id: str):
        """删除文档相关的流水线和任务"""
        # 查询与文档相关的流水线
        pipelines = await pipeline_operations.get_pipelines_by_doc_id(doc_id)

        for pipeline in pipelines:
            pipeline_id = pipeline.get('pipeline_id')
            if pipeline_id:
                # 删除流水线及其任务
                success = await pipeline_operations.delete_pipeline(pipeline_id)
                if success:
                    logger.info(f"删除流水线: {pipeline_id}")
                else:
                    logger.warning(f"流水线删除失败或不存在: {pipeline_id}")

    async def _delete_document_from_neo4j(self, doc_id: str):
        """从Neo4j中删除文档相关的图数据"""
        query = (
            "MATCH (d:Document {doc_id: $doc_id}) "
            "OPTIONAL MATCH (d)-[:CONTAINS]->(n) "
            "DETACH DELETE d, n"
        )
        await neo4j_connection_manager.execute_write_query(query, {"doc_id": doc_id})

    async def _delete_document_from_milvus(self, doc_id: str):
        """从Milvus中删除文档相关的向量数据"""
        await milvus_vector_operations.delete_by_doc_id(doc_id)
        logger.info(f"向Milvus发起了删除doc_id={doc_id}的向量的请求")

    async def _delete_document_from_redis(self, doc_id: str):
        """从Redis中删除文档相关的chunk数据"""
        from src.rag.infrastructure.redis.client import redis_client

        # Redis中chunk数据的key格式：chunks:{doc_id}
        redis_key = f"chunks:{doc_id}"

        # 确保Redis客户端已初始化
        if not redis_client.redis:
            await redis_client.initialize()

        redis_client = redis_client.redis
        if not redis_client:
            logger.warning(f"Redis客户端未初始化，无法删除chunk数据: {redis_key}")
            return

        # 检查key是否存在
        if await redis_client.exists(redis_key):
            # 删除整个set
            deleted_count = await redis_client.delete(redis_key)
            logger.info(f"从Redis删除了chunk数据: key={redis_key}, deleted_count={deleted_count}")
        else:
            logger.info(f"Redis中不存在chunk数据: key={redis_key}")

        logger.info(f"向Redis发起了删除doc_id={doc_id}的chunk数据的请求")

    async def search_documents(
            self,
            query: str,
            page: int = 1,
            page_size: int = 20
    ) -> DocumentListResponse:
        """搜索文档 - 业务逻辑"""

        # 这里可以实现更复杂的搜索逻辑
        # 目前简单地按文件名搜索
        return await self.list_documents(
            page=page,
            page_size=page_size,
            filename_filter=query
        )

    async def create_document_pipeline(self, request: DocumentPipelineRequest) -> CreatePipelineResponse:
        """创建文档处理流水线 - 业务逻辑"""

        # 验证文档是否存在
        doc_metadata = await document_operations.get_document_metadata(request.doc_id)
        if not doc_metadata:
            raise ValueError(f"文档不存在: {request.doc_id}")

        # 使用pipeline_manager创建真正的流水线
        pipeline_id = await pipeline_manager.submit_document_processing_chain(
            doc_id=request.doc_id,
        )

        # 更新文档的处理状态和流水线ID
        await document_operations.update_document_status(
            doc_id=request.doc_id,
            process_status=ProcessStatus.PROCESSING.value,
            task_id=pipeline_id  # 修正参数名
        )

        logger.info(f"成功为文档 {request.doc_id} 创建流水线: {pipeline_id}")

        return CreatePipelineResponse(
            pipeline_id=pipeline_id,
            message=f"文档处理流水线创建成功，文档ID: {request.doc_id}"
        )


# 全局文档服务实例
document_service = DocumentService()
