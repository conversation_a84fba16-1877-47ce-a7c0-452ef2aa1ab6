"""
Chunk 处理路由 - 接口层

📤 职责：
- 参数接收和验证
- 调用分块服务
- 返回标准化响应

三层架构：Route → Service → RAG
"""

from typing import Optional

from fastapi import APIRouter, Query

from src.api.schemas.request.chunks import (
    ChunkSimilaritySearchRequest,
    ChunkKeywordSearchRequest
)
from src.api.schemas.response.chunks import (
    ChunkListResponse,
    ChunkSearchResponse,
    ChunkStatsResponse,
)
from src.api.service.chunk_service import chunk_service

# 创建路由器
router = APIRouter()


@router.get("", response_model=ChunkListResponse)
async def list_chunks(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    doc_id: Optional[str] = Query(None, description="按文档ID过滤"),
    kb_id: Optional[str] = Query(None, description="按知识库ID过滤"),

):
    """分页查看所有chunk - 接口层"""
    return await chunk_service.list_chunks(
        page=page,
        page_size=page_size,
        doc_id=doc_id,
        kb_id=kb_id
    )


@router.post("/similarity-search", response_model=ChunkSearchResponse)
async def search_chunks_by_similarity(
    request: ChunkSimilaritySearchRequest,
     
):
    """通过相似度搜索chunk（使用Milvus向量搜索） - 接口层"""
    return await chunk_service.search_chunks_by_similarity(request)


@router.post("/keyword-search", response_model=ChunkSearchResponse)
async def search_chunks_by_keywords(
    request: ChunkKeywordSearchRequest,
     
):
    """通过关键词搜索chunk（使用Redis文本搜索） - 接口层"""
    return await chunk_service.search_chunks_by_keywords(request)


@router.get("/by-document/{doc_id}", response_model=ChunkListResponse)
async def get_chunks_by_document(
    doc_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
     
):
    """按文档ID查看chunk - 接口层"""
    return await chunk_service.get_chunks_by_document(
        doc_id=doc_id,
        page=page,
        page_size=page_size
    )


@router.get("/stats", response_model=ChunkStatsResponse)
async def get_chunks_stats(
     
):
    """获取chunk统计信息 - 接口层"""
    return await chunk_service.get_chunks_stats()