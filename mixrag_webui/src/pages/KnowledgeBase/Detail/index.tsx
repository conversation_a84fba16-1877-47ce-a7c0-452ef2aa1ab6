import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Tabs, Card, Typography, Spin, message, Breadcrumb } from 'antd';
import { DatabaseOutlined, FileTextOutlined, NodeIndexOutlined, ShareAltOutlined } from '@ant-design/icons';
import PageWrapper from '../../../components/common/PageWrapper';
import DocumentPage from '../../Document';
import ChunkPage from '../../Chunk';
import GraphPage from '../../Graph';
import { fetchKnowledgeBaseDetail } from '../../../api/knowledgeBaseApi';



const KnowledgeBaseDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const [knowledgeBase, setKnowledgeBase] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('document');

  useEffect(() => {
    const loadKnowledgeBaseDetail = async () => {
      if (!id) {
        console.error('知识库ID未定义');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await fetchKnowledgeBaseDetail(id);
        if (response.success) {
          setKnowledgeBase(response.data);
        } else {
          message.error(response.message || '获取知识库详情失败');
        }
      } catch (error: any) {
        console.error('获取知识库详情失败:', error);
        message.error(error.message || '获取知识库详情失败');
      } finally {
        setLoading(false);
      }
    };

    loadKnowledgeBaseDetail();
  }, [id]);

  const tabItems = React.useMemo(() => [
    {
      key: 'document',
      label: (
        <span>
          <FileTextOutlined />
          Document
        </span>
      ),
    },
    {
      key: 'chunk-search',
      label: (
        <span>
          <NodeIndexOutlined />
          Chunk搜索
        </span>
      ),
    },
    {
      key: 'chunk-list',
      label: (
        <span>
          <NodeIndexOutlined />
          Chunk列表
        </span>
      ),
    },
    {
      key: 'graph-statistics',
      label: (
        <span>
          <ShareAltOutlined />
          图统计
        </span>
      ),
    },
    {
      key: 'graph-visualization',
      label: (
        <span>
          <ShareAltOutlined />
          图可视化
        </span>
      ),
    },
    {
      key: 'graph-nodes',
      label: (
        <span>
          <ShareAltOutlined />
          节点
        </span>
      ),
    },
    {
      key: 'graph-edges',
      label: (
        <span>
          <ShareAltOutlined />
          边
        </span>
      ),
    }
  ], []);

  // 渲染当前激活的Tab内容
  const renderTabContent = () => {
    if (!id) {
      return <div>知识库ID未定义</div>;
    }

    switch (activeTab) {
      case 'document':
        return <DocumentPage kb_id={id} />;
      case 'chunk-search':
        return <ChunkPage kb_id={id} activeTab="chunk-search" />;
      case 'chunk-list':
        return <ChunkPage kb_id={id} activeTab="chunk-list" />;
      case 'graph-statistics':
        return <GraphPage kb_id={id} activeTab="graph-statistics" />;
      case 'graph-visualization':
        return <GraphPage kb_id={id} activeTab="graph-visualization" />;
      case 'graph-nodes':
        return <GraphPage kb_id={id} activeTab="graph-nodes" />;
      case 'graph-edges':
        return <GraphPage kb_id={id} activeTab="graph-edges" />;
      default:
        return <DocumentPage kb_id={id} />;
    }
  };

  if (loading) {
    return (
      <PageWrapper>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <Card>
        <Breadcrumb
          style={{ marginBottom: '24px' }}
          items={[
            {
              title: <Link to="/knowledge-base">知识库</Link>
            },
            {
              title: knowledgeBase?.kb_name || '详情'
            }
          ]}
        />

        {/* Tab内容 */}
        <Tabs
          defaultActiveKey="document"
          items={tabItems}
          type="card"
          size="large"
          onChange={setActiveTab}
        />

        {/* 渲染当前激活的Tab内容 */}
        <div style={{ marginTop: '16px' }}>
          {renderTabContent()}
        </div>
      </Card>
    </PageWrapper>
  );
};

export default KnowledgeBaseDetailPage;
