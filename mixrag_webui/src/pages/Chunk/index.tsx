import React, { useState, useEffect } from 'react'
import {
  Card,
  Tabs,
  Input,
  Button,
  Space,
  Table,
  Pagination,
  Select,
  Row,
  Col,
  Statistic,
  Tag,
  Typography,
  message,
  Radio,
  InputNumber,
  Slider
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  FileTextOutlined,
  TableOutlined
} from '@ant-design/icons'
import PageWrapper from '../../components/common/PageWrapper'
import { useChunkManagement } from '../../hooks/useChunkManagement'
import { useDocumentManagement } from '../../hooks/useDocumentManagement'
import './index.less'

const { Search } = Input
const { Option } = Select
const { Text, Paragraph } = Typography

interface ChunkManagementProps {
  kb_id?: string;
  activeTab?: string;
}

const ChunkManagement: React.FC<ChunkManagementProps> = ({ kb_id, activeTab: initialActiveTab = 'search' }) => {
  const [activeTab, setActiveTab] = useState(initialActiveTab);
  const [searchType, setSearchType] = useState('similarity'); // similarity | keywords

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState({
    threshold: 0.2,  // 相似度阈值，默认0.2
    top_k: 10,       // 返回结果数量，默认10
  })

  const {
    // 数据状态
    chunks,
    loading,
    total,
    currentPage,
    pageSize,
    stats,

    // 搜索状态
    searchResults,
    searchLoading,
    searchTotal,

    // 过滤状态
    selectedDocId,

    // 操作方法
    loadChunks,
    loadStats,
    searchBySimilarity,
    searchByKeywords,

    // 状态设置方法
    setCurrentPage,
    setPageSize,
    setSelectedDocId
  } = useChunkManagement(kb_id)

  // 文档管理hook
  const {
    mixrag_documents: documents,
    loading: documentsLoading,
    loadDocuments
  } = useDocumentManagement(kb_id)

  // 组件挂载时加载统计信息和文档列表
  useEffect(() => {
    loadStats()
    loadDocuments()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [kb_id])

  // 当切换到列表tab时加载chunk列表
  useEffect(() => {
    if (activeTab === 'chunk-list') {
      loadChunks()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, currentPage, pageSize, selectedDocId])

  // 处理搜索
  const handleSearch = async (value) => {
    if (!value.trim()) {
      message.warning('请输入搜索内容')
      return
    }

    try {
      if (searchType === 'similarity') {
        await searchBySimilarity(value, {
          threshold: searchParams.threshold,
          top_k: searchParams.top_k
        })
      } else {
        await searchByKeywords(value, {
          limit: searchParams.top_k
        })
      }
    } catch {
      // 错误已在hook中处理
    }
  }

  // 处理搜索参数变化
  const handleSearchParamChange = (key, value) => {
    setSearchParams(prev => ({
      ...prev,
      [key]: value
    }))
  }

  // 处理分页变化
  const handlePageChange = (page, size) => {
    setCurrentPage(page)
    setPageSize(size)
  }

  // 搜索结果表格列
  const searchColumns = [
    {
      title: '块ID',
      dataIndex: 'chunk_id',
      key: 'chunk_id',
      width: 200,
      render: (text) => (
        <Text code copyable={{ text }}>
          {text.substring(0, 18)}...
        </Text>
      )
    },
    {
      title: '文档ID',
      dataIndex: 'doc_id',
      key: 'doc_id',
      width: 200,
      render: (text) => (
        <Text code copyable={{ text }}>
          {text.substring(0, 18)}...
        </Text>
      )
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      className: 'content-cell',
      ellipsis: {
        showTitle: true,
      },
      render: (text) => (
        <div className="content-text">
          {text}
        </div>
      )
    },
    {
      title: '索引',
      dataIndex: 'chunk_index',
      key: 'chunk_index',
      width: 80,
      className: 'number-cell',
      sorter: (a, b) => a.chunk_index - b.chunk_index,
      render: (value) => (
        <Text className="number-cell">{value}</Text>
      )
    },
    ...(searchType === 'similarity' ? [{
      title: '相似度',
      dataIndex: 'similarity',
      key: 'similarity',
      width: 120,
      render: (value) => value ? (
        <Tag
          className="similarity-tag"
          color={value > 0.8 ? 'green' : value > 0.6 ? 'orange' : 'red'}
        >
          {(value * 100).toFixed(1)}%
        </Tag>
      ) : '-',
      sorter: (a, b) => (a.similarity || 0) - (b.similarity || 0)
    }] : [])
  ]

  // 列表表格列
  const listColumns = [
    {
      title: '块ID',
      dataIndex: 'chunk_id',
      key: 'chunk_id',
      width: 200,
      render: (text) => (
        <Text code copyable={{ text }}>
          {text.substring(0, 18)}...
        </Text>
      )
    },
    {
      title: '文档ID',
      dataIndex: 'doc_id',
      key: 'doc_id',
      width: 200,
      render: (text) => (
        <Text code copyable={{ text }}>
          {text.substring(0, 18)}...
        </Text>
      )
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      className: 'content-cell',
      ellipsis: {
        showTitle: true,
      },
      render: (text) => (
        <div className="content-text">
          {text}
        </div>
      )
    },
    {
      title: '索引',
      dataIndex: 'chunk_index',
      key: 'chunk_index',
      width: 80,
      className: 'number-cell',
      sorter: (a, b) => a.chunk_index - b.chunk_index,
      render: (value) => (
        <Text className="number-cell">{value}</Text>
      )
    },
    {
      title: '大小',
      dataIndex: 'chunk_size',
      key: 'chunk_size',
      width: 120,
      className: 'number-cell',
      render: (size) => (
        <Text className="number-cell">{size} 字符</Text>
      ),
      sorter: (a, b) => a.chunk_size - b.chunk_size
    },
  ]

  const searchTabContent = (
    <Card>
      {/* 搜索类型选择 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Radio.Group
            value={searchType}
            onChange={(e) => setSearchType(e.target.value)}
            buttonStyle="solid"
          >
            <Radio.Button value="similarity">相似度搜索</Radio.Button>
            <Radio.Button value="keywords">关键词搜索</Radio.Button>
          </Radio.Group>
        </Col>
        <Col span={18}>
          <Search
            placeholder={
              searchType === 'similarity'
                ? '输入查询文本进行相似度搜索...'
                : '输入关键词进行搜索...'
            }
            allowClear
            enterButton={<SearchOutlined />}
            size="large"
            onSearch={handleSearch}
            loading={searchLoading}
          />
        </Col>
      </Row>

      {/* 搜索参数设置 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>文档选择</Text>
            <Select
              placeholder="选择文档（可选）"
              allowClear
              style={{ width: '100%' }}
              value={selectedDocId}
              onChange={setSelectedDocId}
              loading={documentsLoading}
              showSearch
              optionFilterProp="children"
            >
              {documents.map(doc => (
                <Option key={doc.doc_id} value={doc.doc_id}>
                  {doc.original_filename}
                </Option>
              ))}
            </Select>
          </Space>
        </Col>

        <Col span={6}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Text strong>返回数量 (Top K)</Text>
            <InputNumber
              min={1}
              max={100}
              value={searchParams.top_k}
              onChange={(value) => handleSearchParamChange('top_k', value)}
              style={{ width: '100%' }}
              placeholder="1-100"
            />
          </Space>
        </Col>

        {searchType === 'similarity' && (
          <Col span={12}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>相似度阈值: {searchParams.threshold}</Text>
              <Slider
                min={0}
                max={1}
                step={0.01}
                value={searchParams.threshold}
                onChange={(value) => handleSearchParamChange('threshold', value)}
                marks={{
                  0: '0',
                  0.2: '0.2',
                  0.5: '0.5',
                  0.8: '0.8',
                  1: '1'
                }}
              />
            </Space>
          </Col>
        )}
      </Row>

      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ marginBottom: 16, padding: 8, background: '#f0f0f0', fontSize: '12px' }}>
          <div>搜索结果数量: {searchResults?.length || 0}</div>
          <div>搜索总数: {searchTotal}</div>
          <div>加载状态: {searchLoading ? '加载中' : '已完成'}</div>
        </div>
      )}

      {/* 搜索结果表格 */}
      <Table
        className="search-results-table"
        columns={searchColumns}
        dataSource={searchResults}
        rowKey="chunk_id"
        loading={searchLoading}
        size="middle"
        bordered
        pagination={{
          current: 1,
          pageSize: 10,
          total: searchTotal,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条搜索结果`,
          pageSizeOptions: ['10', '20', '50']
        }}
        scroll={{ x: 1000 }}
      />
    </Card>
  )

  const listTabContent = (
    <Card>
      {/* 过滤器 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Select
            placeholder="选择文档过滤"
            allowClear
            style={{ width: '100%' }}
            value={selectedDocId}
            onChange={setSelectedDocId}
            loading={documentsLoading}
            showSearch
            optionFilterProp="children"
          >
            {documents.map(doc => (
              <Option key={doc.doc_id} value={doc.doc_id}>
                {doc.original_filename}
              </Option>
            ))}
          </Select>
        </Col>
        <Col span={18}>
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={() => {
                loadChunks()
                loadStats()
              }}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Chunk列表表格 */}
      <Table
        className="list-table"
        columns={listColumns}
        dataSource={chunks}
        rowKey="chunk_id"
        loading={loading}
        size="middle"
        bordered
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          onChange: handlePageChange,
          onShowSizeChange: handlePageChange,
          pageSizeOptions: ['10', '20', '50', '100']
        }}
        scroll={{ x: 1200 }}
      />
    </Card>
  )

  return (
    <PageWrapper>
      <div className="chunk-management">
        

        {/* 主要内容 */}
        <div style={{ padding: '0 24px' }}>
          {activeTab === 'chunk-search' && searchTabContent}
          {activeTab === 'chunk-list' && listTabContent}
        </div>
      </div>
    </PageWrapper>
  )
}

export default ChunkManagement 