#!/usr/bin/env python3
"""
清理所有存储系统中的数据
通过加载 .env 获取各个存储系统的连接信息，然后清空数据（保留表结构）
"""

import asyncio
import os
import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量 - 使用与系统一致的方式
project_root = Path(__file__).parent.parent
env_path = project_root / ".env"
if env_path.exists():
    from dotenv import load_dotenv
    load_dotenv(env_path)
    print(f"✅ 已加载环境变量: {env_path}")
else:
    print(f"⚠️ 未找到.env文件: {env_path}")

from src.rag.infrastructure import (
    postgre_sql_client, redis_client, min_io_client, object_operations,
    milvus_vector_operations, neo4j_connection_manager
)
from pymilvus import utility, Collection


async def cleanup_postgresql():
    """清理PostgreSQL数据"""
    print("🗄️ 清理PostgreSQL数据...")

    try:
        # 初始化连接
        await postgre_sql_client.initialize()
        print("PostgreSQL连接初始化完成")

        async with postgre_sql_client.pool.acquire() as conn:
            print("获取数据库连接成功")

            # 直接清空已知的表，按依赖关系顺序
            tables_to_clean = [
                'mixrag_documents',
                'mixrag_knowledge_base',
                'mixrag_model_manage',
                'mixrag_pipeline',
                'mixrag_pipeline_task',
            ]

            for table_name in tables_to_clean:
                print(f"正在清空表: {table_name}")

                # 检查表是否存在
                table_exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = $1
                    )
                """, table_name)

                if table_exists:
                    # 使用DELETE而不是TRUNCATE，避免权限问题
                    await conn.execute(f"DELETE FROM {table_name}")
                    print(f"✅ 已清空表: {table_name}")
                else:
                    print(f"表不存在: {table_name}")

        print("✅ PostgreSQL清理完成")
    except Exception as e:
        traceback.print_exc()
        print(f"❌ PostgreSQL连接失败，跳过清理: {e}")
        print("💡 请检查PostgreSQL服务是否正常运行")


async def cleanup_redis():
    """清理Redis数据"""
    print("📦 清理Redis数据...")

    try:
        # 初始化连接
        await redis_client.initialize()

        # 获取当前数据库编号
        current_db = redis_client.redis.connection_pool.connection_kwargs.get('db', 0)
        print(f"当前Redis数据库: {current_db}")

        # 清空当前数据库
        await redis_client.redis.flushdb()

        # 验证清空结果
        key_count = await redis_client.redis.dbsize()
        print(f"✅ Redis清理完成，剩余键数量: {key_count}")
    except Exception as e:
        print(f"❌ Redis清理失败: {e}")
        import traceback
        traceback.print_exc()
        print("💡 请检查Redis服务是否正常运行")


async def cleanup_milvus():
    """清理Milvus数据"""
    print("🔍 清理Milvus数据...")

    try:
        # 直接使用milvus_client来初始化连接
        from src.rag.infrastructure.milvus.client import milvus_client
        await milvus_client.initialize()

        collection_name = milvus_vector_operations.collection_name
        print(f"集合名称: {collection_name}")

        # 使用连接池删除集合
        async with milvus_vector_operations._connection_pool.get_connection() as connection_alias:
            # 检查集合是否存在
            if utility.has_collection(collection_name, using=connection_alias):
                # 删除集合
                utility.drop_collection(collection_name, using=connection_alias)
                print(f"✅ 已删除集合: {collection_name}")
            else:
                print(f"集合不存在: {collection_name}")

        # 重新初始化以创建新的空集合
        await milvus_client.initialize()

        # 验证集合状态
        async with milvus_vector_operations._connection_pool.get_connection() as connection_alias:
            if utility.has_collection(collection_name, using=connection_alias):
                collection = Collection(collection_name, using=connection_alias)
                entity_count = collection.num_entities
                print(f"✅ Milvus清理完成，实体数量: {entity_count}")
            else:
                print("集合未正确重建")
    except Exception as e:
        print(f"❌ Milvus清理失败: {e}")
        import traceback
        traceback.print_exc()
        print("💡 请检查Milvus服务是否正常运行")


async def cleanup_neo4j():
    """清理Neo4j数据"""
    print("🕸️ 清理Neo4j数据...")

    try:
        # 初始化连接
        await neo4j_connection_manager.initialize()

        # 统计当前数据量
        query = 'MATCH (n) RETURN count(n) as node_count'
        result = await neo4j_connection_manager.execute_query(query)
        node_count = result[0]['node_count'] if result else 0
        print(f"发现 {node_count} 个节点")

        query = 'MATCH ()-[r]->() RETURN count(r) as rel_count'
        result = await neo4j_connection_manager.execute_query(query)
        rel_count = result[0]['rel_count'] if result else 0
        print(f"发现 {rel_count} 个关系")

        # 删除所有节点和关系
        query = "MATCH (n) DETACH DELETE n"
        await neo4j_connection_manager.execute_write_query(query)
        print("✅ 已删除所有节点和关系")

        # 验证清理结果
        query = 'MATCH (n) RETURN count(n) as node_count'
        result = await neo4j_connection_manager.execute_query(query)
        remaining_nodes = result[0]['node_count'] if result else 0

        query = 'MATCH ()-[r]->() RETURN count(r) as rel_count'
        result = await neo4j_connection_manager.execute_query(query)
        remaining_rels = result[0]['rel_count'] if result else 0

        print(f"✅ Neo4j清理完成，剩余: {remaining_nodes}个节点，{remaining_rels}个关系")
    except Exception as e:
        print(f"❌ Neo4j清理失败: {e}")
        import traceback
        traceback.print_exc()
        print("💡 请检查Neo4j服务是否正常运行")


async def cleanup_minio():
    """清理MinIO数据"""
    print("📁 清理MinIO数据...")

    try:
        # 初始化连接
        await min_io_client.initialize()

        # 获取所有对象
        objects = await object_operations.list_files()
        print(f"找到 {len(objects)} 个对象")

        # 删除所有对象
        for obj in objects:
            await object_operations.delete_file(obj['object_key'])
            print(f"✅ 已删除文件: {obj['object_key']}")

        # 验证清理结果
        remaining_objects = await object_operations.list_files()
        print(f"✅ MinIO清理完成，剩余对象: {len(remaining_objects)}")
    except Exception as e:
        print(f"❌ MinIO清理失败: {e}")
        import traceback
        traceback.print_exc()
        print("💡 请检查MinIO服务是否正常运行")


async def main():
    """主清理函数"""
    print("🚨 开始清理所有存储系统数据...")
    print("📋 清理范围: Redis, Milvus, Neo4j, MinIO")
    print()

    # 串行执行清理操作，跳过PostgreSQL
    print("--- PostgreSQL ---")
    await cleanup_postgresql()
    print()

    print("--- Redis ---")
    await cleanup_redis()
    print()

    print("--- Milvus ---")
    await cleanup_milvus()
    print()

    print("--- Neo4j ---")
    await cleanup_neo4j()
    print()

    print("--- MinIO ---")
    await cleanup_minio()
    print()

    print("🎉 存储系统数据清理完成！(PostgreSQL除外)")


if __name__ == "__main__":
    asyncio.run(main())